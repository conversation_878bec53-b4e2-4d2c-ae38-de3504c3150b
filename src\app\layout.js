import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "NameCardAI - Your Name. Reinvented.",
  description: "AR-enhanced digital business cards that revolutionize professional networking. Share stunning, interactive 3D profiles via QR, NFC, or camera scan—no app required.",
  keywords: "digital business cards, AR business cards, networking, QR codes, NFC, professional networking, 3D cards",
  authors: [{ name: "NameCardAI Team" }],
  creator: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  publisher: "NameCardAI",
  openGraph: {
    title: "NameCardAI - Your Name. Reinvented.",
    description: "AR-enhanced digital business cards that revolutionize professional networking.",
    url: "https://namecardai.com",
    siteName: "NameCardAI",
    images: [
      {
        url: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
        width: 1200,
        height: 630,
        alt: "NameCardAI - AR-Enhanced Digital Business Cards",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "NameCardAI - Your Name. Reinvented.",
    description: "AR-enhanced digital business cards that revolutionize professional networking.",
    images: ["https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp"],
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon.ico",
    apple: "/favicon.ico",
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
